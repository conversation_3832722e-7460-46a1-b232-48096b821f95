

#include <yaEngineNext/nxt_engine.h>
#include <yaEngineNext/nxt_mbuf.h>
#include <yaProtoRecord/precord.h>
#include <yaBasicUtils/allocator.h>


#include <cstdint>
#include <gtest/gtest.h>
#include <vector>
#include <gmock/gmock.h>

extern "C" {
#include "asn1_discovery.h"
#include "per_decoder.h"
}

// H.225.0 RAS
//     RasMessage: admissionRequest (9)
//         admissionRequest
//             requestSeqNum: 24410
//             callType: pointToPoint (0)
//                 pointToPoint: NULL
//             endpointIdentifier: 52B880FC00000003
//             destinationInfo: 1 item
//                 Item 0
//                     DestinationInfo item: dialledDigits (0)
//                         dialledDigits: 4997450629
//             srcInfo: 1 item
//                 Item 0
//                     AliasAddress: h323-ID (1)
//                         h323-ID: H-SRV
//             srcCallSignalAddress: ipAddress (0)
//                 ipAddress
//                     ip: ***********
//                     port: 58535
//             bandWidth: 2621
//             callReferenceValue: 3436
//             conferenceID: 8a3016ff-2901-0010-091c-5c987aa98517
//             0... .... activeMC: False
//             .0.. .... answerCall: False
//             1... .... canMapAlias: True
//             callIdentifier
//                 guid: 8a3016ff-2901-0010-091b-5c987aa98517
//             gatekeeperIdentifier: Gk7206
//             0... .... willSupplyUUIEs: False
std::vector<uint8_t> RasMessage_admissionRequest_[] = {
  0x26, 0x90, 0x5f, 0x59, 0x03, 0xc0, 0x00, 0x35,
  0x00, 0x32, 0x00, 0x42, 0x00, 0x38, 0x00, 0x38,
  0x00, 0x30, 0x00, 0x46, 0x00, 0x43, 0x00, 0x30,
  0x00, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x30,
  0x00, 0x30, 0x00, 0x30, 0x00, 0x33, 0x01, 0x04,
  0x80, 0x7c, 0xca, 0x78, 0x39, 0x5c, 0x01, 0x40,
  0x04, 0x00, 0x48, 0x00, 0x2d, 0x00, 0x53, 0x00,
  0x52, 0x00, 0x56, 0x00, 0x55, 0x5a, 0x61, 0x41,
  0xe4, 0xa7, 0x40, 0x0a, 0x3d, 0x0d, 0x6c, 0x8a,
  0x30, 0x16, 0xff, 0x29, 0x01, 0x00, 0x10, 0x09,
  0x1c, 0x5c, 0x98, 0x7a, 0xa9, 0x85, 0x17, 0x08,
  0xe4, 0x20, 0x00, 0x01, 0x80, 0x11, 0x00, 0x8a,
  0x30, 0x16, 0xff, 0x29, 0x01, 0x00, 0x10, 0x09,
  0x1b, 0x5c, 0x98, 0x7a, 0xa9, 0x85, 0x17, 0x0d,
  0x0a, 0x00, 0x47, 0x00, 0x6b, 0x00, 0x37, 0x00,
  0x32, 0x00, 0x30, 0x00, 0x36, 0x01, 0x00
};


class SNMPAsn1Test : public ::testing::Test {
protected:
    void SetUp() override {
        alloc = ya_allocator_get_default();
        nxt_engine_config_t config = {.linkName = "eth", .trailerName = nullptr};
        engine = nxt_engine_create(&config);
        ASSERT_NE(engine, nullptr);
    }

    void TearDown() override {
        if (engine) {
            nxt_engine_destroy(engine);
        }
    }

    ya_allocator_t* alloc;
    nxt_engine_t* engine;
};


TEST_F(SNMPAsn1Test, ASN1_ParseGetRequest) {
    asn_dec_rval_t decode_result;
    RasMessage_t *RsaInfo = NULL;

    decode_result = uper_decode_complete(0, &asn_DEF_RasMessage, (void **)&RsaInfo, RasMessage_admissionRequest_.data(), RasMessage_admissionRequest_.size());
    if (decode_result.code == RC_OK) {
        printf("H225RAS: UPER decode complete successful\n");
    } else {
        printf("H225RAS: UPER decode complete failed (consumed: %zu bytes)\n", decode_result.consumed);
        if (RsaInfo) {
            ASN_STRUCT_FREE(asn_DEF_RasMessage, RsaInfo);
            RsaInfo = NULL;
        }

}